<!DOCTYPE html><html lang="ar" dir="rtl"><head>
<meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Weilix Chat — صوت ونص</title>
<style>
  body{font-family:system-ui;margin:0;background:#0b1020;color:#eaf0ff}
  .wrap{max-width:900px;margin:30px auto;padding:16px}
  .row{display:flex;gap:8px;flex-wrap:wrap;align-items:center}
  input,button,textarea,select{background:#101836;border:1px solid #243055;border-radius:10px;color:#eaf0ff}
  textarea{flex:1;min-height:110px;padding:10px}
  input,select{padding:10px}
  button{padding:10px 16px;cursor:pointer}
  .log{white-space:pre-wrap;background:#0d1530;border:1px dashed #2b375f;border-radius:10px;padding:10px;margin-top:12px;min-height:120px}
  .pill{padding:4px 10px;border:1px solid #2a3563;background:#0f1838;border-radius:999px;color:#b7c4ea}
  .mic{display:inline-flex;align-items:center;gap:6px}
  .mic button.rec{background:#1f2b63;border-color:#4052a5}
  .mic button.rec.live{background:#7a1621;border-color:#c62839}
  .muted{color:#9fb0d9}
</style>
</head>
<body>
<div class="wrap">
  <h2>واجهة محادثة Weilix — صوت ونص</h2>

  <div class="row" style="margin-bottom:8px">
    <label>قاعدة API:&nbsp;</label>
    <input id="base" value="http://127.0.0.1:8080" style="width:320px">
    <button onclick="ping()">فحص</button>
    <span id="status" class="pill">—</span>
  </div>

  <!-- إعدادات الصوت -->
  <div class="row" style="margin-bottom:8px">
    <div class="mic">
      <label>لغة الاستماع:</label>
      <select id="recLang">
        <option value="ar-SA">العربية (السعودية)</option>
        <option value="ar-EG">العربية (مصر)</option>
        <option value="en-US">English (US)</option>
        <option value="en-GB">English (UK)</option>
      </select>
      <label>وضع الاستماع:</label>
      <select id="recMode">
        <option value="ptt" selected>اضغط وتحدث (Push-to-talk)</option>
        <option value="cont">مستمر (Continuous)</option>
      </select>
      <button id="btnRec" class="rec" onclick="toggleRec()">🎙️ بدء الاستماع</button>
      <span id="recInfo" class="muted">المايك غير نشط</span>
    </div>
    <div class="mic">
      <label>صوت الرد:</label>
      <select id="ttsVoice"></select>
      <label>السرعة:</label>
      <input id="ttsRate" type="number" min="0.5" max="2" step="0.1" value="1" style="width:80px">
      <label>تحكم:</label>
      <button onclick="ttsStop()">⏹️ إيقاف النطق</button>
    </div>
  </div>

  <!-- مربع الإدخال اليدوي -->
  <textarea id="prompt" placeholder="تحدث بالمايك أو اكتب رسالتك إلى Weilix هنا…"></textarea>
  <div class="row" style="margin-top:8px">
    <button onclick="send()">إرسال</button>
    <label><input type="checkbox" id="autoSpeak" checked> نطق الرد تلقائيًا</label>
    <label><input type="checkbox" id="wsMode"> استخدام WebSocket</label>
  </div>

  <div id="out" class="log"></div>
</div>

<script>
  // عناصر مساعدة
  const $ = id => document.getElementById(id);
  const base = () => $('base').value.replace(/\/$/, '');

  // WebSocket
  let websocket = null;
  let wsConnected = false;

  async function ping(){
    const s = $('status');
    try{
      const r = await fetch(base() + '/v1/health');
      const j = await r.json();
      s.textContent = 'OK • ' + j.version;
    }catch{
      s.textContent = 'فشل الاتصال';
    }
  }

  // === WebSocket ===
  function connectWS() {
    if (websocket) return;
    
    const wsUrl = base().replace('http', 'ws') + '/ws';
    websocket = new WebSocket(wsUrl);
    
    websocket.onopen = () => {
      wsConnected = true;
      $('status').textContent += ' | WS متصل';
    };
    
    websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleWSMessage(data);
    };
    
    websocket.onclose = () => {
      wsConnected = false;
      websocket = null;
      $('status').textContent = $('status').textContent.replace(' | WS متصل', '');
    };
  }
  
  function handleWSMessage(data) {
    const out = $('out');
    const timestamp = new Date().toLocaleTimeString('ar');
    
    if (data.type === 'voice_response') {
      out.textContent += `\n[${timestamp}] 🎙️ ${data.prompt}\n📢 ${data.output}\n`;
      if ($('autoSpeak').checked) speak(data.output);
    } else if (data.type === 'connection_established') {
      out.textContent += `\n[${timestamp}] 🔗 ${data.message}\n`;
    }
  }

  // === إرسال للنظام والحصول على رد ===
  async function send(){
    const out = $('out');
    let prompt = $('prompt').value.trim();
    if(!prompt){ out.textContent = 'اكتب شيئًا أو استخدم المايك…'; return; }
    
    // استخدام WebSocket إذا كان مفعلاً
    if ($('wsMode').checked && wsConnected) {
      const message = {
        type: 'voice_input',
        prompt: prompt,
        broadcast: false
      };
      websocket.send(JSON.stringify(message));
      $('prompt').value = '';
      return;
    }
    
    // HTTP عادي
    out.textContent = 'جارٍ الإرسال…';
    try{
      const r = await fetch(base() + '/v1/infer', {
        method:'POST', headers:{'Content-Type':'application/json'},
        body: JSON.stringify({ prompt, context: [] })
      });
      const j = await r.json();
      out.textContent = JSON.stringify(j, null, 2);
      if ($('autoSpeak').checked && j && j.output) speak(j.output);
      // إفراغ خانة النص بعد الإرسال في وضع الضغط والتحدث
      if ($('recMode').value === 'ptt') $('prompt').value = '';
    }catch(e){
      out.textContent = 'خطأ: ' + e;
    }
  }

  // === TTS (النطق) ===
  let voices = [];
  function populateVoices(){
    voices = speechSynthesis.getVoices();
    const sel = $('ttsVoice'); sel.innerHTML = '';
    // افضّل أصوات العربية إن وجدت
    const preferred = v => /ar|arabic/i.test(v.lang);
    const sorted = [...voices].sort((a,b)=> (preferred(b) - preferred(a)));
    for(const v of sorted){
      const opt = document.createElement('option');
      opt.value = v.name;
      opt.textContent = `${v.name} — ${v.lang}`;
      sel.appendChild(opt);
    }
  }
  if ('speechSynthesis' in window){
    populateVoices();
    window.speechSynthesis.onvoiceschanged = populateVoices;
  } else {
    console.warn('TTS غير مدعوم في هذا المتصفح.');
  }

  function speak(text){
    if (!('speechSynthesis' in window)) return;
    const u = new SpeechSynthesisUtterance(text);
    const name = $('ttsVoice').value;
    const voice = voices.find(v => v.name === name);
    if (voice) u.voice = voice;
    let rate = parseFloat($('ttsRate').value || '1');
    if (isNaN(rate)) rate = 1;
    u.rate = Math.max(0.5, Math.min(2, rate));
    u.lang = (voice && voice.lang) || 'ar-SA';
    // أوقف أي نطق سابق قبل البدء
    window.speechSynthesis.cancel();
    window.speechSynthesis.speak(u);
  }
  function ttsStop(){ if ('speechSynthesis' in window) window.speechSynthesis.cancel(); }

  // === STT (الاستماع) ===
  const Rec = window.SpeechRecognition || window.webkitSpeechRecognition;
  let rec = null, recActive = false;

  function initRec(){
    if (!Rec){ $('recInfo').textContent = 'التعرّف الصوتي غير مدعوم (جرّب Chrome/Edge).'; return; }
    rec = new Rec();
    rec.interimResults = true;
    rec.continuous = ($('recMode').value === 'cont');
    rec.lang = $('recLang').value;
    rec.onstart = ()=>{ recActive = true; $('btnRec').classList.add('live'); $('recInfo').textContent = 'المايك يعمل… تحدث الآن'; };
    rec.onend = ()=>{ recActive = false; $('btnRec').classList.remove('live'); $('recInfo').textContent = 'المايك متوقف'; if ($('recMode').value === 'cont' && wantsCont) safeStart(); };
    rec.onerror = (e)=>{ $('recInfo').textContent = 'خطأ مايك: ' + e.error; };
    rec.onresult = (e)=>{
      let finalText = '', interim='';
      for (let i = e.resultIndex; i < e.results.length; i++){
        const t = e.results[i][0].transcript;
        if (e.results[i].isFinal) finalText += t;
        else interim += t;
      }
      $('prompt').value = (finalText || interim || '').trim();
      // في وضع الاستماع المستمر: أرسل كل نتيجة نهائية تلقائيا
      if (finalText && $('recMode').value === 'cont'){ send(); }
    };
  }

  let wantsCont = false;
  async function safeStart(){
    try{ rec && rec.start(); }catch(e){ /* قد يرمي لو بدأ مسبقا */ }
  }
  function toggleRec(){
    if (!rec){ initRec(); if (!rec) return; }
    rec.lang = $('recLang').value;
    rec.continuous = ($('recMode').value === 'cont');

    if (recActive){
      try{ rec.stop(); }catch{}
      $('recInfo').textContent = 'إيقاف المايك…';
      return;
    }
    if ($('recMode').value === 'cont') wantsCont = true; else wantsCont = false;
    safeStart();
  }

  // مراقبة تغيير وضع WebSocket
  $('wsMode').addEventListener('change', () => {
    if ($('wsMode').checked) {
      connectWS();
    } else if (websocket) {
      websocket.close();
    }
  });

  // تهيئة أولية
  ping();
  initRec();
</script>
</body></html>
