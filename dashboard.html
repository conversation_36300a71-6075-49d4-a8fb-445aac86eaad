<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>لوحة تحكم ويليكس</title>
  <style>
    :root { --bg:#0b1020; --card:#141a2f; --ink:#eaf0ff; --muted:#a8b3cf; --accent:#4da3ff; --ok:#38c172; --warn:#ffb400; --err:#ef5753; }
    *{box-sizing:border-box}
    body{margin:0;background:var(--bg);color:var(--ink);font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,Arial}
    header{display:flex;gap:12px;align-items:center;justify-content:space-between;padding:16px 20px;background:linear-gradient(180deg,#0f1730,#0b1020)}
    header h1{margin:0;font-size:20px}
    .base{display:flex;gap:10px;align-items:center;color:var(--muted)}
    .base input{width:340px;max-width:60vw}
    input,select,button,textarea{background:#0e1530;color:var(--ink);border:1px solid #273055;border-radius:10px;padding:10px 12px}
    button{cursor:pointer;background:linear-gradient(180deg,#1b2a5a,#142046);border:1px solid #324174}
    button:hover{filter:brightness(1.08)}
    main{display:grid;grid-template-columns:1fr 1fr;gap:16px;padding:16px}
    section{background:var(--card);border:1px solid #222a45;border-radius:14px;padding:14px}
    h2{margin:0 0 10px 0;font-size:16px;color:#dce6ff}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    table{width:100%;border-collapse:collapse;margin-top:10px}
    th,td{border-bottom:1px solid #243055;padding:8px 6px;text-align:right;color:#d7e0ff}
    th{color:#94a3c7;font-weight:600;background:#101835}
    .status{padding:2px 8px;border-radius:999px;font-size:12px;border:1px solid #33406c}
    .ACTIVE{background:rgba(56,193,114,.12);border-color:#27593e;color:#7be2a7}
    .PAUSED{background:rgba(255,180,0,.12);border-color:#7a5c0d;color:#ffd174}
    .RETIRED{background:rgba(239,87,83,.12);border-color:#7a1e1c;color:#ff9a97}
    .pill{display:inline-flex;align-items:center;gap:6px;padding:4px 10px;background:#0f1838;border:1px solid #2a3563;border-radius:999px;color:#b7c4ea;font-size:12px}
    .muted{color:var(--muted)}
    .grid2{display:grid;grid-template-columns:1fr 1fr;gap:10px}
    .ok{color:var(--ok)}.warn{color:var(--warn)}.err{color:var(--err)}
    .small{font-size:12px}
    .log{white-space:pre-wrap;background:#0c1228;border:1px dashed #2b375f;border-radius:10px;padding:8px;min-height:44px}
    .loading{opacity:0.6;pointer-events:none}
    .spinner{display:inline-block;width:12px;height:12px;border:2px solid #2a3563;border-top:2px solid var(--accent);border-radius:50%;animation:spin 1s linear infinite}
    @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
  </style>
</head>
<body>
  <header>
    <h1>لوحة تحكم ويليكس</h1>
    <div class="base">
      <span class="muted small">قاعدة API:</span>
      <input id="base" value="http://127.0.0.1:8080" />
      <button onclick="ping()">فحص الصحة</button>
      <button onclick="autoRefresh()" title="تحديث جميع البيانات">🔄 تحديث شامل</button>
      <span id="health" class="small pill">بانتظار الفحص…</span>
    </div>
  </header>

  <main>
    <section>
      <h2>الوكلاء (ANAT / INANA)</h2>
      <div class="row">
        <input id="agentCode" placeholder="مثال: anat-1" />
        <select id="agentKind">
          <option value="ANAT">ANAT (علني)</option>
          <option value="INANA">INANA (سري)</option>
        </select>
        <button onclick="createAgent()">+ إضافة وكيل</button>
        <button onclick="loadAgents()">تحديث</button>
      </div>
      <table>
        <thead><tr><th>#</th><th>الكود</th><th>النوع</th><th>الحالة</th><th>أُنشئ</th></tr></thead>
        <tbody id="agentsBody"><tr><td colspan="5" class="muted">لا بيانات بعد…</td></tr></tbody>
      </table>
      <div id="agentsLog" class="log small muted"></div>
    </section>

    <section>
      <h2>المهام</h2>
      <div class="grid2">
        <div class="row">
          <input id="taskAgentId" placeholder="Agent ID (مثال: 1)" />
          <input id="taskTitle" placeholder="عنوان المهمة" />
        </div>
        <div class="row">
          <input id="taskDetails" placeholder='تفاصيل JSON (اختياري): {"note":"..."}' />
          <select id="taskPriority">
            <option value="1">أولوية عالية (1)</option>
            <option value="2" selected>عادية (2)</option>
            <option value="3">منخفضة (3)</option>
          </select>
        </div>
      </div>
      <div class="row" style="margin-top:8px">
        <button onclick="createTask()">+ إضافة مهمة</button>
        <input id="filterAgentId" placeholder="تصفية حسب Agent ID" style="width:180px" />
        <button onclick="loadTasks()">تحديث</button>
      </div>
      <table>
        <thead><tr><th>#</th><th>Agent</th><th>العنوان</th><th>تفاصيل</th><th>أولوية</th><th>الحالة</th><th>أُنشئت</th><th>إجراءات</th></tr></thead>
        <tbody id="tasksBody"><tr><td colspan="8" class="muted">لا بيانات بعد…</td></tr></tbody>
      </table>
      <div id="tasksLog" class="log small muted"></div>
    </section>

    <section>
      <h2>🎙️ ويدجت صوتي متقدم</h2>
      <div class="grid2" style="margin-bottom:10px">
        <div class="row">
          <label>لغة الاستماع:</label>
          <select id="voiceLang">
            <option value="ar-SA">العربية (السعودية)</option>
            <option value="ar-EG">العربية (مصر)</option>
            <option value="en-US">English (US)</option>
          </select>
          <button id="voiceBtn" onclick="toggleVoiceMode()">🎙️ بدء الاستماع</button>
        </div>
        <div class="row">
          <label>صوت الرد:</label>
          <select id="ttsVoice"></select>
          <label>السرعة:</label>
          <input id="ttsRate" type="number" min="0.5" max="2" step="0.1" value="1" style="width:80px">
          <button onclick="stopTTS()">⏹️ إيقاف النطق</button>
        </div>
      </div>
      <div class="row" style="margin-bottom:8px">
        <label><input type="checkbox" id="autoSpeak" checked> نطق الردود تلقائيًا</label>
        <label><input type="checkbox" id="continuousMode"> وضع الاستماع المستمر</label>
        <label><input type="checkbox" id="broadcastMode"> وضع البث (إذاعة جميع الردود)</label>
        <span id="voiceStatus" class="pill">المايك متوقف</span>
      </div>
      <textarea id="inferPrompt" placeholder="تحدث بالمايك أو اكتب رسالة لويليكس…" style="width:100%;min-height:80px"></textarea>
      <div class="row" style="margin-top:8px">
        <button onclick="callInfer()">إرسال</button>
        <button onclick="clearInferLog()">مسح السجل</button>
      </div>
      <div id="inferLog" class="log"></div>
    </section>

    <section>
      <h2>حالة قاعدة البيانات</h2>
      <div class="row">
        <button onclick="dbPing()">DB Ping</button>
        <span id="dbStatus" class="pill">—</span>
      </div>
      <div class="small muted" style="margin-top:8px">تأكد من تشغيل MySQL عبر XAMPP.</div>
    </section>
  </main>

  <script>
    const $ = (id) => document.getElementById(id);
    const base = () => $("base").value.replace(/\/$/, "");

    function setStatus(el, ok, text) {
      el.innerHTML = text || (ok ? 'جاهز' : 'فشل');
      el.style.borderColor = ok ? '#27593e' : '#7a1e1c';
      el.style.background = ok ? 'rgba(56,193,114,.12)' : 'rgba(239,87,83,.12)';
      el.style.color = ok ? '#7be2a7' : '#ff9a97';
    }

    function setLoading(el, text = 'جارٍ التحميل…') {
      el.innerHTML = `<span class="spinner"></span> ${text}`;
      el.style.borderColor = '#324174';
      el.style.background = 'rgba(77,163,255,.12)';
      el.style.color = '#4da3ff';
    }

    async function ping(){
      const el = $("health");
      setLoading(el, 'فحص الصحة…');
      try{
        const r = await fetch(`${base()}/v1/health`);
        const j = await r.json();
        setStatus(el, true, `✅ OK • ${j.version}`);
      }catch(e){ setStatus(el,false,'❌ فشل الاتصال'); }
    }

    async function dbPing(){
      const el = $("dbStatus");
      setLoading(el, 'فحص قاعدة البيانات…');
      try{
        const r = await fetch(`${base()}/v1/db-ping`);
        const j = await r.json();
        const icon = j.db === 'ok' ? '✅' : '❌';
        setStatus(el, j.db === 'ok', `${icon} DB: ${j.db}`);
      }catch(e){ setStatus(el,false,'❌ DB: خطأ في الاتصال'); }
    }

    async function loadAgents(){
      const log = $("agentsLog");
      log.textContent = '';
      try{
        const r = await fetch(`${base()}/v1/agents`);
        const data = await r.json();
        const tb = $("agentsBody");
        tb.innerHTML = '';
        if (!Array.isArray(data) || data.length === 0){
          tb.innerHTML = '<tr><td colspan="5" class="muted">لا وكلاء بعد…</td></tr>';
          return;
        }
        for(const a of data){
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td>${a.id}</td>
            <td>${a.code}</td>
            <td>${a.kind}</td>
            <td><span class="status ${a.status}">${a.status}</span></td>
            <td>${a.created_at || ''}</td>`;
          tb.appendChild(tr);
        }
        log.textContent = `تم تحميل ${data.length} وكيلًا.`;
      }catch(e){
        log.textContent = 'خطأ في التحميل: ' + e;
      }
    }

    async function createAgent(){
      const code = $("agentCode").value.trim();
      const kind = $("agentKind").value;
      const log = $("agentsLog");
      if(!code){ log.textContent = 'أدخل كود الوكيل.'; return; }
      try{
        const r = await fetch(`${base()}/v1/agents`,{
          method:'POST', headers:{'Content-Type':'application/json'},
          body: JSON.stringify({code, kind})
        });
        if(!r.ok){
          const j = await r.json().catch(()=>({detail:r.statusText}));
          log.textContent = 'فشل الإضافة: ' + (j.detail || r.status);
          return;
        }
        const a = await r.json();
        log.textContent = `تمت إضافة الوكيل #${a.id} (${a.code}).`;
        $("agentCode").value='';
        loadAgents();
      }catch(e){ log.textContent = 'خطأ شبكة: ' + e; }
    }

    async function loadTasks(){
      const log = $("tasksLog");
      const agentId = $("filterAgentId").value.trim();
      log.textContent = '';
      try{
        const url = agentId ? `${base()}/v1/tasks?agent_id=${encodeURIComponent(agentId)}` : `${base()}/v1/tasks`;
        const r = await fetch(url);
        const data = await r.json();
        const tb = $("tasksBody");
        tb.innerHTML = '';
        if (!Array.isArray(data) || data.length === 0){
          tb.innerHTML = '<tr><td colspan="8" class="muted">لا مهام بعد…</td></tr>';
          return;
        }
        for(const t of data){
          const tr = document.createElement('tr');
          const details = (t.details ? JSON.stringify(t.details) : '—');
          tr.innerHTML = `
            <td>${t.id}</td>
            <td>${t.agent_id}</td>
            <td>${t.title}</td>
            <td style="max-width:320px;overflow-wrap:anywhere">${details}</td>
            <td>${t.priority}</td>
            <td>${t.state}</td>
            <td>${t.created_at || ''}</td>
            <td>
              <button onclick="setTaskState(${t.id}, 'RUNNING')" style="font-size:11px;padding:2px 6px">تشغيل</button>
              <button onclick="setTaskState(${t.id}, 'DONE')" style="font-size:11px;padding:2px 6px">تم</button>
              <button onclick="setTaskState(${t.id}, 'FAILED')" style="font-size:11px;padding:2px 6px">فشل</button>
              <button onclick="viewTaskLogs(${t.id})" style="font-size:11px;padding:2px 6px">سجلات</button>
            </td>`;
          tb.appendChild(tr);
        }
        log.textContent = `تم تحميل ${data.length} مهمة.`;
      }catch(e){ log.textContent = 'خطأ في التحميل: ' + e; }
    }

    async function createTask(){
      const agent_id = parseInt($("taskAgentId").value,10);
      const title    = $("taskTitle").value.trim();
      const detailsS = $("taskDetails").value.trim();
      const priority = parseInt($("taskPriority").value,10);
      const log = $("tasksLog");
      if(!agent_id || !title){ log.textContent = 'أدخل Agent ID وعنوان المهمة.'; return; }
      let details=null;
      if(detailsS){
        try{ details = JSON.parse(detailsS); }
        catch{ log.textContent = 'تفاصيل JSON غير صالحة.'; return; }
      }
      try{
        const r = await fetch(`${base()}/v1/tasks`,{
          method:'POST', headers:{'Content-Type':'application/json'},
          body: JSON.stringify({agent_id, title, details, priority})
        });
        if(!r.ok){
          const j = await r.json().catch(()=>({detail:r.statusText}));
          log.textContent = 'فشل الإضافة: ' + (j.detail || r.status);
          return;
        }
        const t = await r.json();
        log.textContent = `تمت إضافة المهمة #${t.id} للوكيل ${t.agent_id}.`;
        $("taskTitle").value='';
        $("taskDetails").value='';
        loadTasks();
      }catch(e){ log.textContent = 'خطأ شبكة: ' + e; }
    }

    async function callInfer(){
      const prompt = $("inferPrompt").value.trim();
      const out = $("inferLog");
      out.textContent = 'جارٍ الإرسال…';
      try{
        const r = await fetch(`${base()}/v1/infer`,{
          method:'POST', headers:{'Content-Type':'application/json'},
          body: JSON.stringify({prompt, context: []})
        });
        const j = await r.json();
        out.textContent = JSON.stringify(j, null, 2);
      }catch(e){ out.textContent = 'خطأ: ' + e; }
    }

    // تحديث تلقائي كل 30 ثانية
    function autoRefresh() {
      ping();
      dbPing();
      loadAgents();
      loadTasks();
    }

    // تشغيل أولي
    autoRefresh();

    // تحديث تلقائي كل 30 ثانية
    setInterval(autoRefresh, 30000);

    // دوال تحديث حالة المهام
    async function setTaskState(taskId, to) {
      const log = $("tasksLog");
      try{
        const r = await fetch(`${base()}/v1/tasks/${taskId}/state`,{
          method:'PATCH',
          headers:{'Content-Type':'application/json'},
          body: JSON.stringify({ to_state: to, note: "via dashboard" })
        });
        if(!r.ok){
          const j = await r.json().catch(()=>({detail:r.statusText}));
          log.textContent = `فشل تحديث الحالة: ${j.detail || r.status}`;
          return;
        }
        log.textContent = `✅ تم تغيير حالة المهمة #${taskId} إلى ${to}`;
        loadTasks();
      }catch(e){
        log.textContent = 'خطأ شبكة: ' + e;
      }
    }

    // عرض سجلات المهمة
    async function viewTaskLogs(taskId) {
      const log = $("tasksLog");
      try{
        const r = await fetch(`${base()}/v1/tasks/${taskId}/logs`);
        if(!r.ok){
          const j = await r.json().catch(()=>({detail:r.statusText}));
          log.textContent = `فشل جلب السجلات: ${j.detail || r.status}`;
          return;
        }
        const logs = await r.json();
        if(logs.length === 0){
          log.textContent = `📋 لا توجد سجلات للمهمة #${taskId}`;
          return;
        }

        let logText = `📋 سجلات المهمة #${taskId}:\n`;
        logs.forEach(l => {
          const from = l.from_state || 'جديد';
          const note = l.note ? ` (${l.note})` : '';
          logText += `• ${from} → ${l.to_state}${note} - ${new Date(l.created_at).toLocaleString('ar')}\n`;
        });
        log.textContent = logText;
      }catch(e){
        log.textContent = 'خطأ شبكة: ' + e;
      }
    }

    // === ويدجت صوتي متقدم ===
    const SpeechRec = window.SpeechRecognition || window.webkitSpeechRecognition;
    let recognition = null;
    let isListening = false;
    let voices = [];
    let broadcastInterval = null;
    let websocket = null;
    let wsConnected = false;

    // تهيئة أصوات TTS
    function initVoices() {
      voices = speechSynthesis.getVoices();
      const sel = $('ttsVoice');
      sel.innerHTML = '';

      // ترتيب الأصوات: العربية أولاً
      const sortedVoices = [...voices].sort((a, b) => {
        const aIsArabic = /ar|arabic/i.test(a.lang);
        const bIsArabic = /ar|arabic/i.test(b.lang);
        return bIsArabic - aIsArabic;
      });

      for (const voice of sortedVoices) {
        const option = document.createElement('option');
        option.value = voice.name;
        option.textContent = `${voice.name} — ${voice.lang}`;
        sel.appendChild(option);
      }
    }

    // تهيئة التعرف الصوتي
    function initSpeechRecognition() {
      if (!SpeechRec) {
        $('voiceStatus').textContent = 'التعرف الصوتي غير مدعوم (جرب Chrome/Edge)';
        $('voiceBtn').disabled = true;
        return;
      }

      recognition = new SpeechRec();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = $('voiceLang').value;

      recognition.onstart = () => {
        isListening = true;
        $('voiceStatus').textContent = '🎙️ يستمع...';
        $('voiceStatus').style.background = 'rgba(56,193,114,.12)';
        $('voiceStatus').style.borderColor = '#27593e';
        $('voiceStatus').style.color = '#7be2a7';
        $('voiceBtn').textContent = '⏹️ إيقاف الاستماع';
      };

      recognition.onend = () => {
        isListening = false;
        $('voiceStatus').textContent = 'المايك متوقف';
        $('voiceStatus').style.background = 'rgba(239,87,83,.12)';
        $('voiceStatus').style.borderColor = '#7a1e1c';
        $('voiceStatus').style.color = '#ff9a97';
        $('voiceBtn').textContent = '🎙️ بدء الاستماع';

        // إعادة تشغيل تلقائي في الوضع المستمر
        if ($('continuousMode').checked) {
          setTimeout(() => {
            if (!isListening) startListening();
          }, 1000);
        }
      };

      recognition.onerror = (event) => {
        $('voiceStatus').textContent = `خطأ: ${event.error}`;
        $('voiceStatus').style.background = 'rgba(239,87,83,.12)';
        $('voiceStatus').style.borderColor = '#7a1e1c';
        $('voiceStatus').style.color = '#ff9a97';
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // تحديث النص في المربع
        const currentText = $('inferPrompt').value;
        if (finalTranscript) {
          $('inferPrompt').value = currentText + finalTranscript + ' ';

          // إرسال تلقائي في الوضع المستمر
          if ($('continuousMode').checked && finalTranscript.trim()) {
            setTimeout(() => callInfer(), 500);
          }
        } else if (interimTranscript) {
          // عرض النص المؤقت
          $('inferPrompt').value = currentText + interimTranscript;
        }
      };
    }

    function startListening() {
      if (!recognition) return;
      try {
        recognition.lang = $('voiceLang').value;
        recognition.start();
      } catch (e) {
        console.warn('خطأ في بدء الاستماع:', e);
      }
    }

    function stopListening() {
      if (recognition && isListening) {
        recognition.stop();
      }
    }

    function toggleVoiceMode() {
      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    }

    // نطق النص
    function speak(text) {
      if (!('speechSynthesis' in window)) return;

      // إيقاف أي نطق سابق
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      const selectedVoice = $('ttsVoice').value;
      const voice = voices.find(v => v.name === selectedVoice);

      if (voice) utterance.voice = voice;

      const rate = parseFloat($('ttsRate').value) || 1;
      utterance.rate = Math.max(0.5, Math.min(2, rate));
      utterance.lang = voice ? voice.lang : 'ar-SA';

      speechSynthesis.speak(utterance);
    }

    function stopTTS() {
      if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
      }
    }

    // تحديث دالة callInfer لدعم النطق التلقائي
    const originalCallInfer = callInfer;
    callInfer = async function() {
      const prompt = $("inferPrompt").value.trim();
      const out = $("inferLog");

      if (!prompt) {
        out.textContent = 'اكتب شيئًا أو استخدم المايك…';
        return;
      }

      out.textContent = 'جارٍ الإرسال…';

      try {
        const r = await fetch(`${base()}/v1/infer`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ prompt, context: [] })
        });

        const j = await r.json();
        const timestamp = new Date().toLocaleTimeString('ar');
        out.textContent = `[${timestamp}] ${JSON.stringify(j, null, 2)}`;

        // نطق الرد تلقائيًا إذا كان مفعلاً
        if ($('autoSpeak').checked && j && j.output) {
          speak(j.output);
        }

        // مسح النص في الوضع المستمر
        if ($('continuousMode').checked) {
          $('inferPrompt').value = '';
        }

      } catch (e) {
        out.textContent = 'خطأ: ' + e;
      }
    };

    function clearInferLog() {
      $('inferLog').textContent = '';
    }

    // وضع البث - مراقبة مستمرة وإذاعة الردود
    function toggleBroadcastMode() {
      if ($('broadcastMode').checked) {
        startBroadcastMode();
      } else {
        stopBroadcastMode();
      }
    }

    function startBroadcastMode() {
      if (broadcastInterval) return;

      // بدء الاستماع المستمر
      if (!$('continuousMode').checked) {
        $('continuousMode').checked = true;
      }

      if (!isListening) {
        startListening();
      }

      // مراقبة دورية للتحديثات
      broadcastInterval = setInterval(() => {
        // يمكن إضافة منطق مراقبة إضافي هنا
        if (!isListening && $('continuousMode').checked) {
          startListening();
        }
      }, 5000);

      $('inferLog').textContent += '\n🔴 وضع البث نشط - مراقبة مستمرة\n';
    }

    function stopBroadcastMode() {
      if (broadcastInterval) {
        clearInterval(broadcastInterval);
        broadcastInterval = null;
      }
      $('inferLog').textContent += '\n⏹️ تم إيقاف وضع البث\n';
    }

    // مراقبة تغيير وضع البث
    $('broadcastMode').addEventListener('change', toggleBroadcastMode);

    // === WebSocket للتدفق المباشر ===
    function connectWebSocket() {
      const wsUrl = base().replace('http', 'ws') + '/ws';

      try {
        websocket = new WebSocket(wsUrl);

        websocket.onopen = () => {
          wsConnected = true;
          $('voiceStatus').textContent += ' | WS متصل';
          console.log('WebSocket متصل');
        };

        websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
          } catch (e) {
            console.error('خطأ في تحليل رسالة WebSocket:', e);
          }
        };

        websocket.onclose = () => {
          wsConnected = false;
          $('voiceStatus').textContent = $('voiceStatus').textContent.replace(' | WS متصل', '');
          console.log('WebSocket منقطع');

          // إعادة الاتصال التلقائي
          setTimeout(() => {
            if ($('broadcastMode').checked) {
              connectWebSocket();
            }
          }, 3000);
        };

        websocket.onerror = (error) => {
          console.error('خطأ WebSocket:', error);
        };

      } catch (e) {
        console.error('فشل في إنشاء WebSocket:', e);
      }
    }

    function handleWebSocketMessage(data) {
      const timestamp = new Date().toLocaleTimeString('ar');

      switch (data.type) {
        case 'connection_established':
          $('inferLog').textContent += `\n[${timestamp}] 🔗 ${data.message}\n`;
          break;

        case 'voice_response':
          $('inferLog').textContent += `\n[${timestamp}] 🎙️ سؤال: ${data.prompt}\n📢 رد: ${data.output}\n`;
          if ($('autoSpeak').checked) {
            speak(data.output);
          }
          break;

        case 'broadcast_message':
          $('inferLog').textContent += `\n[${timestamp}] 📡 بث: ${data.prompt}\n📢 رد: ${data.output}\n`;
          if ($('autoSpeak').checked) {
            speak(data.output);
          }
          break;

        case 'inference_response':
          $('inferLog').textContent += `\n[${timestamp}] 💬 ${data.prompt}\n📢 ${data.output}\n`;
          if ($('autoSpeak').checked && $('broadcastMode').checked) {
            speak(data.output);
          }
          break;

        case 'error':
          $('inferLog').textContent += `\n[${timestamp}] ❌ خطأ: ${data.message}\n`;
          break;

        case 'pong':
          console.log('WebSocket ping successful');
          break;
      }

      // تمرير تلقائي للأسفل
      $('inferLog').scrollTop = $('inferLog').scrollHeight;
    }

    function sendWebSocketMessage(message) {
      if (websocket && wsConnected) {
        websocket.send(JSON.stringify(message));
        return true;
      }
      return false;
    }

    function disconnectWebSocket() {
      if (websocket) {
        websocket.close();
        websocket = null;
        wsConnected = false;
      }
    }

    // تحديث دالة callInfer لدعم WebSocket
    const originalCallInfer2 = callInfer;
    callInfer = async function() {
      const prompt = $("inferPrompt").value.trim();
      const out = $("inferLog");

      if (!prompt) {
        out.textContent = 'اكتب شيئًا أو استخدم المايك…';
        return;
      }

      // إذا كان WebSocket متصل، استخدمه
      if (wsConnected && $('broadcastMode').checked) {
        const message = {
          type: 'voice_input',
          prompt: prompt,
          broadcast: $('broadcastMode').checked
        };

        if (sendWebSocketMessage(message)) {
          // مسح النص في الوضع المستمر
          if ($('continuousMode').checked) {
            $('inferPrompt').value = '';
          }
          return;
        }
      }

      // استخدام HTTP العادي كبديل
      out.textContent = 'جارٍ الإرسال…';

      try {
        const r = await fetch(`${base()}/v1/infer`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ prompt, context: [] })
        });

        const j = await r.json();
        const timestamp = new Date().toLocaleTimeString('ar');
        out.textContent = `[${timestamp}] ${JSON.stringify(j, null, 2)}`;

        // نطق الرد تلقائيًا إذا كان مفعلاً
        if ($('autoSpeak').checked && j && j.output) {
          speak(j.output);
        }

        // مسح النص في الوضع المستمر
        if ($('continuousMode').checked) {
          $('inferPrompt').value = '';
        }

      } catch (e) {
        out.textContent = 'خطأ: ' + e;
      }
    };

    // تحديث وضع البث ليدعم WebSocket
    function startBroadcastMode() {
      if (broadcastInterval) return;

      // الاتصال بـ WebSocket
      connectWebSocket();

      // بدء الاستماع المستمر
      if (!$('continuousMode').checked) {
        $('continuousMode').checked = true;
      }

      if (!isListening) {
        startListening();
      }

      // مراقبة دورية للتحديثات
      broadcastInterval = setInterval(() => {
        // ping WebSocket للتأكد من الاتصال
        if (wsConnected) {
          sendWebSocketMessage({ type: 'ping' });
        }

        if (!isListening && $('continuousMode').checked) {
          startListening();
        }
      }, 10000);

      $('inferLog').textContent += '\n🔴 وضع البث نشط - مراقبة مستمرة + WebSocket\n';
    }

    function stopBroadcastMode() {
      if (broadcastInterval) {
        clearInterval(broadcastInterval);
        broadcastInterval = null;
      }

      disconnectWebSocket();
      $('inferLog').textContent += '\n⏹️ تم إيقاف وضع البث\n';
    }

    // تهيئة الويدجت الصوتي
    if ('speechSynthesis' in window) {
      initVoices();
      speechSynthesis.onvoiceschanged = initVoices;
    }

    initSpeechRecognition();

  </script>
</body>
</html>
